"use client"

import React, { useState } from "react"
import { Check, ChevronDown, Globe } from "lucide-react"
import { useLanguage } from "@/contexts/LanguageContext"
import { LanguageCode } from "@/lib/language-constants"
import { cn } from "@/utils"

interface LanguageSelectorProps {
  variant?: "default" | "compact" | "minimal"
  showFlag?: boolean
  showNativeName?: boolean
  className?: string
}

export function LanguageSelector({
  variant = "default",
  showFlag = true,
  showNativeName = true,
  className,
}: LanguageSelectorProps) {
  const { currentLanguage, language, changeLanguage, supportedLanguages, t } =
    useLanguage()
  const [isOpen, setIsOpen] = useState(false)

  const handleLanguageChange = (languageCode: LanguageCode) => {
    changeLanguage(languageCode)
    setIsOpen(false)
  }

  const renderTrigger = () => {
    switch (variant) {
      case "compact":
        return (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "flex items-center gap-2 rounded-md border border-gray-200 px-3 py-2 transition-colors hover:border-gray-300",
              "bg-white text-sm font-medium hover:bg-gray-50",
              className,
            )}
          >
            {showFlag && <span className="text-lg">{language.flag}</span>}
            <span className="uppercase">{currentLanguage}</span>
            <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform",
                isOpen && "rotate-180",
              )}
            />
          </button>
        )

      case "minimal":
        return (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "flex items-center gap-1 rounded-md p-2 transition-colors hover:bg-gray-100",
              className,
            )}
          >
            <Globe className="h-4 w-4" />
            <ChevronDown
              className={cn(
                "h-3 w-3 transition-transform",
                isOpen && "rotate-180",
              )}
            />
          </button>
        )

      default:
        return (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "flex items-center gap-3 rounded-lg border border-gray-200 px-4 py-2 transition-colors hover:border-gray-300",
              "min-w-[160px] justify-between bg-white hover:bg-gray-50",
              className,
            )}
          >
            <div className="flex items-center gap-2">
              {showFlag && <span className="text-xl">{language.flag}</span>}
              <span className="font-medium">
                {showNativeName ? language.nativeName : language.name}
              </span>
            </div>
            <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform",
                isOpen && "rotate-180",
              )}
            />
          </button>
        )
    }
  }

  return (
    <div className="relative">
      {renderTrigger()}

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute left-0 top-full z-50 mt-2 max-h-60 w-full min-w-[200px] overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg">
            <div className="p-2">
              <div className="mb-1 px-2 py-1 text-xs font-medium text-gray-500">
                {t("common", "selectLanguage")}
              </div>

              {Object.entries(supportedLanguages).map(([code, lang]) => (
                <button
                  key={code}
                  onClick={() => handleLanguageChange(code as LanguageCode)}
                  className={cn(
                    "flex w-full items-center gap-3 rounded-md px-3 py-2 text-left transition-colors hover:bg-gray-100",
                    currentLanguage === code && "bg-blue-50 text-blue-600",
                  )}
                >
                  <span className="text-lg">{lang.flag}</span>
                  <div className="flex-1">
                    <div className="font-medium">{lang.nativeName}</div>
                    <div className="text-xs text-gray-500">{lang.name}</div>
                  </div>
                  {currentLanguage === code && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Simplified version for quick access
export function LanguageSelectorSimple() {
  return (
    <LanguageSelector
      variant="compact"
      showNativeName={false}
      className="text-xs"
    />
  )
}

// Minimal version for tight spaces
export function LanguageSelectorMinimal() {
  return (
    <LanguageSelector
      variant="minimal"
      className="text-gray-600 hover:text-gray-900"
    />
  )
}
