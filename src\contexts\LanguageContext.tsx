'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getCookie, setCookie } from 'cookies-next'
import { 
  LanguageCode, 
  Language, 
  SUPPORTED_LANGUAGES, 
  DEFAULT_LANGUAGE,
  LANGUAGE_COOKIE_NAME,
  LANGUAGE_COOKIE_MAX_AGE,
  TRANSLATIONS,
  TranslationKey,
  TranslationNestedKey
} from '@/lib/language-constants'

interface LanguageContextType {
  currentLanguage: LanguageCode
  language: Language
  isLoading: boolean
  changeLanguage: (languageCode: LanguageCode) => void
  t: (key: TranslationKey, nestedKey?: TranslationNestedKey<TranslationKey>) => string
  supportedLanguages: typeof SUPPORTED_LANGUAGES
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

interface LanguageProviderProps {
  children: React.ReactNode
  initialLanguage?: LanguageCode
}

export function LanguageProvider({ children, initialLanguage }: LanguageProviderProps) {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(
    initialLanguage || DEFAULT_LANGUAGE
  )
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get language from cookie or detect from browser/geo
    const detectLanguage = async () => {
      try {
        // First check cookie
        const savedLanguage = getCookie(LANGUAGE_COOKIE_NAME) as LanguageCode
        if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
          setCurrentLanguage(savedLanguage)
          setIsLoading(false)
          return
        }

        // If no saved language and no initial language, try to detect
        if (!initialLanguage) {
          try {
            const response = await fetch('/api/detect-location')
            const data = await response.json()
            const detectedLanguage = data.detectedLanguage as LanguageCode
            
            if (SUPPORTED_LANGUAGES[detectedLanguage]) {
              setCurrentLanguage(detectedLanguage)
              // Save to cookie
              setCookie(LANGUAGE_COOKIE_NAME, detectedLanguage, {
                maxAge: LANGUAGE_COOKIE_MAX_AGE,
                path: '/',
                sameSite: 'lax',
                secure: process.env.NODE_ENV === 'production'
              })
            }
          } catch (error) {
            console.error('Failed to detect language:', error)
            // Fallback to browser language
            const browserLang = navigator.language.split('-')[0] as LanguageCode
            if (SUPPORTED_LANGUAGES[browserLang]) {
              setCurrentLanguage(browserLang)
            }
          }
        }
      } catch (error) {
        console.error('Language detection error:', error)
      } finally {
        setIsLoading(false)
      }
    }

    detectLanguage()
  }, [initialLanguage])

  const changeLanguage = (languageCode: LanguageCode) => {
    if (!SUPPORTED_LANGUAGES[languageCode]) {
      console.error(`Unsupported language: ${languageCode}`)
      return
    }

    setCurrentLanguage(languageCode)
    
    // Save to cookie
    setCookie(LANGUAGE_COOKIE_NAME, languageCode, {
      maxAge: LANGUAGE_COOKIE_MAX_AGE,
      path: '/',
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })

    // Optional: reload page to apply language changes
    // window.location.reload()
  }

  // Translation function
  const t = (key: TranslationKey, nestedKey?: TranslationNestedKey<TranslationKey>): string => {
    try {
      const translations = TRANSLATIONS[currentLanguage] || TRANSLATIONS[DEFAULT_LANGUAGE]
      
      if (nestedKey) {
        // @ts-ignore - TypeScript can't infer the nested structure properly
        return translations[key]?.[nestedKey] || TRANSLATIONS[DEFAULT_LANGUAGE][key]?.[nestedKey] || `${key}.${nestedKey}`
      }
      
      // @ts-ignore - TypeScript can't infer the structure properly
      return translations[key] || TRANSLATIONS[DEFAULT_LANGUAGE][key] || key
    } catch (error) {
      console.error('Translation error:', error)
      return nestedKey ? `${key}.${nestedKey}` : key
    }
  }

  const language = SUPPORTED_LANGUAGES[currentLanguage]

  const value: LanguageContextType = {
    currentLanguage,
    language,
    isLoading,
    changeLanguage,
    t,
    supportedLanguages: SUPPORTED_LANGUAGES
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Hook for simple translation without context
export function useTranslation() {
  const { t } = useLanguage()
  return { t }
}
