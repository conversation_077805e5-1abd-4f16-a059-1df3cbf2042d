"use client"

import "@twicpics/components/style.css"

import { twicpics } from "@/config-global"
import { AuthProvider } from "@/guard/context/appwrite"
import AuthGuard from "@/guard/guard/auth-guard"
// TwicPics Components importation
import { Toaster as ToasterSonner } from "@/components/ui/sonner"
import { installTwicpics } from "@twicpics/components/react"

import { GSAPProvider } from "@/animations/gsap-provider"
import { WebVitals } from "@/components/web-vitals"
import * as Sentry from "@sentry/nextjs"
import { LightboxProvider } from "./lightbox-ui"
import { LanguageProvider } from "@/contexts/LanguageContext"

type Props = {
  children: React.ReactNode
  isProtected?: boolean
}

// TwicPics Components configuration (see Setup Options)
installTwicpics({
  // domain is mandatory
  domain: twicpics.domain,
})

export function RootProvider({ children, isProtected = false }: Props) {
  return (
    <Sentry.ErrorBoundary>
      <WebVitals />

      <LanguageProvider>
        <GSAPProvider>
          <LightboxProvider>
            <AuthProvider>
              {isProtected ? <AuthGuard>{children}</AuthGuard> : children}
            </AuthProvider>

            <ToasterSonner position="bottom-center" />
          </LightboxProvider>
        </GSAPProvider>
      </LanguageProvider>
    </Sentry.ErrorBoundary>
  )
}
